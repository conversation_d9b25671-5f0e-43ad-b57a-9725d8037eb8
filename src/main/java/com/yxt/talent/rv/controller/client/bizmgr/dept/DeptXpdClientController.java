package com.yxt.talent.rv.controller.client.bizmgr.dept;

import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.Validate;
import com.yxt.talent.rv.application.dept.DeptQryAppService;
import com.yxt.talent.rv.application.xpd.xpd.XpdAppService;
import com.yxt.talent.rv.application.xpd.result.XpdResultAppService;
import com.yxt.talent.rv.controller.client.bizmgr.dept.query.DeptProjectClientQuery;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptPrjClientVO;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptPrjUserClientVO;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptXpdStatisticsClientVO;
import com.yxt.talent.rv.controller.manage.org.query.OrgProfileUserDimGridQuery;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdDimCombGridResultVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdDimCombTableResultVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdResultUserDimDetailVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.config.swagger.SwaggerPageQuery;
import com.yxt.talent.rv.infrastructure.service.auth.AuthorizationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_DEFAULT;
import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "我的部门-部门看板", description = "我的部门-部门看板")
@RequestMapping(value = "")
public class DeptXpdClientController {

    private final AuthService authService;
    private final AuthorizationService authorizationService;
    private final XpdResultAppService xpdResultAppService;
    private final DeptQryAppService deptQryAppService;
    private final XpdAppService xpdAppService;

    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    @Operation(summary = "xpd项目-盘点宫格", description = "用户部门看板和组织看板中，使用人员盘点维度结果聚合表数据进行组织盘点结果展示, 使用默认九宫格模板的标签配置和维度组合配置")
    @PostMapping(value = "/client/dept/xpd/grid/user", consumes = MEDIATYPE, produces = MEDIATYPE)
    public List<XpdDimCombGridResultVO> getGlobalXpdUserDimGrids(
        HttpServletRequest request, @Validated @RequestBody OrgProfileUserDimGridQuery criteria) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        String orgId = userCache.getOrgId();
        log.info("LOG20313:orgId={}, criteria={} ", orgId, BeanHelper.bean2Json(criteria, NON_DEFAULT));
        criteria.setScopeDeptIds(deptQryAppService.getSubDeptIds(orgId, criteria.getScopeDeptIds()));
        log.info("LOG20253:orgId={}, criteria={} ", orgId, BeanHelper.bean2Json(criteria, NON_DEFAULT));
        Validate.isNotBlank(criteria.getGridId(), ExceptionKeys.XPD_GRID_ID_EMPTY);
        Validate.isNotBlank(criteria.getDimCombId(), ExceptionKeys.XPD_DIM_COMB_ID_EMPTY);
        return xpdResultAppService.getGlobalXpdUserDimGrids(orgId, criteria);
    }

    // 点击某个格子，获取格子中的人员信息，分页列表
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    @Operation(summary = "xpd项目-盘点宫格人员分页列表", description = "用户部门看板和组织看板中，使用人员盘点维度结果聚合表数据进行组织盘点结果展示, 使用默认九宫格模板的标签配置和维度组合配置")
    @PostMapping(value = "/client/dept/xpd/grid/cell/user", produces = MEDIATYPE, consumes = MEDIATYPE)
    public PagingList<XpdDimCombTableResultVO> getGlobalXpdCellUserResults(
        HttpServletRequest request, @Validated @RequestBody OrgProfileUserDimGridQuery criteria) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        String orgId = userCache.getOrgId();
        log.info("LOG20323:orgId={}, criteria={} ", orgId, BeanHelper.bean2Json(criteria, NON_DEFAULT));
        criteria.setScopeDeptIds(deptQryAppService.getSubDeptIds(orgId, criteria.getScopeDeptIds()));
        log.info("LOG20283:orgId={}, criteria={} ", orgId, BeanHelper.bean2Json(criteria, NON_DEFAULT));
        Validate.isNotNull(criteria.getCellIndex(), ExceptionKeys.XPD_CELL_INDEX_EMPTY);
        return xpdResultAppService.getGlobalXpdCellUserResults(orgId, criteria);
    }

    @SwaggerPageQuery
    @Operation(summary = "xpd项目分页")
    @PostMapping(value = "/client/dept/xpd", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public PagingList<DeptPrjClientVO> getMyDeptProjects(
        @Validated @RequestBody DeptProjectClientQuery criteria) {
        UserCacheDetail userCache = authService.getUserCacheDetail(ApiUtil.getRequestByContext());
        String orgId = userCache.getOrgId();
        String userId = userCache.getUserId();
        authorizationService.checkAndFillUserDeptManageScope(orgId, userId, criteria);
        return xpdAppService.getMyDeptProjects(orgId, userId, criteria);
    }

    @Operation(summary = "xpd项目聚合统计指标")
    @PostMapping(value = "/client/dept/xpd/{xpdId}/statistics", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public DeptXpdStatisticsClientVO getMineProjectStatistics(
        @PathVariable String xpdId, @Validated @RequestBody DeptProjectClientQuery criteria) {
        UserCacheDetail userCache = authService.getUserCacheDetail(ApiUtil.getRequestByContext());
        String orgId = userCache.getOrgId();
        String userId = userCache.getUserId();
        criteria.setPrjId(xpdId);
        // 统计不跟着搜索条件走，这里将前端传过来的搜索条件置空
        criteria.setScopeDeptIds(new ArrayList<>());
        criteria.setScopeUserIds(new ArrayList<>());
        authorizationService.checkAndFillUserDeptManageScope(orgId, userId, criteria);
        return xpdAppService.getMyDeptProjectStatistics(orgId, userId, criteria, userCache.getLocale());
    }

    @SwaggerPageQuery
    @Operation(summary = "xpd员工盘点结果分页")
    @PostMapping(value = "/client/dept/xpd/{xpdId}/users", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public PagingList<DeptPrjUserClientVO> getMyDeptProjectUser(
        @PathVariable String xpdId, @Validated @RequestBody DeptProjectClientQuery criteria) {
        UserCacheDetail userCache = authService.getUserCacheDetail(ApiUtil.getRequestByContext());
        String orgId = userCache.getOrgId();
        String userId = userCache.getUserId();
        criteria.setPrjId(xpdId);
        authorizationService.checkAndFillUserDeptManageScope(orgId, userId, criteria);
        return xpdAppService.getMyDeptProjectUser(orgId, userId, criteria, userCache.getLocale());
    }

    @ResponseStatus(OK)
    @Operation(summary = "人员各维度所属等级详情")
    @GetMapping("/client/dept/xpd/{userId}/dim-detail")
    public XpdResultUserDimDetailVO getUserDimDetail(HttpServletRequest request, @PathVariable String userId) {
        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
//        return xpdResultAppService.getUserDimDetail(currentUser, xpdId, userId);
        // TODO orgProfileAppService 编写对应的方法
        return null;
    }

}
